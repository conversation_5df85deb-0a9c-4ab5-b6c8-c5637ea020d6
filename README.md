# Kopia strony zwiekszenergie.pl/pro/energia/start

## Opis projektu

To jest kopia strony internetowej https://zwiekszenergie.pl/pro/energia/start - strony landing page dla przedsiębiorców oferującej szkolenia i konsultacje dotyczące zwiększania energii, podnoszenia testosteronu i redukcji stresu.

## Struktura projektu

```
pochwala/
├── index.html          # Główna strona HTML
├── styles.css          # Arkusz stylów CSS
├── script.js           # Funkcjonalność JavaScript
└── README.md           # Dokumentacja projektu
```

## Funkcjonalności

### 1. Responsywny design
- Strona jest w pełni responsywna i dostosowuje się do różnych rozmiarów ekranów
- Optymalizacja dla urządzeń mobilnych, tabletów i desktopów

### 2. <PERSON><PERSON><PERSON><PERSON> strony
- **Hero Section**: Główny nagłówek z atrakcyjnym gradientem
- **Kroki**: Dwuetapowy proces (obejrzyj szkolenie + umów konsultację)
- **Video**: Placeholder dla bezpłatnego szkolenia wideo
- **Korzyści**: Cztery główne korzyści (energia, testosteron, stres, focus)
- **Opinie**: Sekcja z opiniami klientów
- **CTA**: Wezwanie do działania z przyciskiem kontaktowym

### 3. Interaktywność
- Animacje scroll-based (elementy pojawiają się podczas przewijania)
- Interaktywne przyciski CTA
- Modal z formularzem konsultacji
- Placeholder video z animacją ładowania

### 4. Formularz konsultacji
- Pełny formularz kontaktowy w modalnym oknie
- Walidacja pól wymaganych
- Animacje i efekty wizualne
- Symulacja wysyłania formularza

### 5. Tracking i analityka
- Placeholder dla Facebook Pixel
- System trackowania eventów
- Monitoring scroll depth
- Tracking interakcji użytkownika

## Technologie

- **HTML5**: Semantyczna struktura strony
- **CSS3**: Nowoczesne style z gradientami, animacjami i flexbox/grid
- **JavaScript (ES6+)**: Interaktywność i funkcjonalność
- **Google Fonts**: Czcionka Manrope dla lepszej typografii

## Stylizacja

### Kolory
- Główny gradient: `#667eea` → `#764ba2`
- CTA button: `#e74c3c` → `#c0392b`
- Tekst: `#2c3e50`, `#666`
- Tło: `#f8f9fa`, `#ecf0f1`

### Czcionki
- **Manrope**: Główna czcionka (wagi 200-800)
- Responsywne rozmiary czcionek

### Animacje
- Fade-in przy scroll
- Hover effects na przyciskach i kartach
- Loading animations
- Modal transitions

## Instalacja i uruchomienie

1. Sklonuj lub pobierz pliki projektu
2. Otwórz `index.html` w przeglądarce internetowej
3. Strona jest gotowa do użycia - nie wymaga serwera

## Dostosowywanie

### Zmiana kolorów
Edytuj zmienne CSS w pliku `styles.css`:
```css
/* Główne kolory */
--primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
--cta-color: #e74c3c;
--text-color: #2c3e50;
```

### Dodanie prawdziwego video
Zastąp `.video-placeholder` w `index.html` prawdziwym embedem video:
```html
<iframe src="YOUR_VIDEO_URL" width="100%" height="400"></iframe>
```

### Integracja z prawdziwą analityką
W pliku `script.js` w funkcji `trackEvent()` dodaj prawdziwe wywołania:
```javascript
// Facebook Pixel
if (typeof fbq !== 'undefined') {
    fbq('track', eventName, eventData);
}

// Google Analytics
if (typeof gtag !== 'undefined') {
    gtag('event', eventName, eventData);
}
```

### Integracja z backend
Funkcja `handleConsultationForm()` w `script.js` może być zmodyfikowana do wysyłania danych na serwer:
```javascript
fetch('/api/consultation', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
});
```

## Optymalizacja SEO

Strona zawiera podstawowe elementy SEO:
- Meta title i description
- Semantyczne tagi HTML
- Structured data (można dodać JSON-LD)
- Optymalizacja obrazów (placeholder dla favicon)

## Zgodność z przeglądarkami

Strona jest kompatybilna z:
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Licencja

Ten projekt jest kopią edukacyjną strony zwiekszenergie.pl utworzoną w celach demonstracyjnych.

## Kontakt

W przypadku pytań lub sugestii dotyczących tego projektu, skontaktuj się z autorem.

---

**Uwaga**: To jest kopia/rekonstrukcja strony utworzona w celach edukacyjnych. Wszystkie prawa do oryginalnej treści należą do właścicieli zwiekszenergie.pl.
