// Main JavaScript functionality for zwiekszenergie.pl copy

document.addEventListener('DOMContentLoaded', function() {
    console.log('Website loaded successfully');
    
    // Initialize all functionality
    initScrollAnimations();
    initCTAButtons();
    initVideoPlaceholder();
    initFormHandling();
    initAnalytics();
});

// Scroll animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animatedElements = document.querySelectorAll('.step-card, .benefit-item, .video-placeholder');
    animatedElements.forEach(el => {
        el.classList.add('animate-on-scroll');
        observer.observe(el);
    });
}

// CTA Button functionality
function initCTAButtons() {
    const ctaButtons = document.querySelectorAll('.cta-button');
    
    ctaButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
            
            // Track click event
            trackEvent('CTA_Click', {
                button_text: this.textContent.trim(),
                page_section: getPageSection(this)
            });
            
            // Show consultation form or redirect
            showConsultationForm();
        });
    });
}

// Video placeholder functionality
function initVideoPlaceholder() {
    const videoPlaceholder = document.querySelector('.video-placeholder');
    
    if (videoPlaceholder) {
        videoPlaceholder.addEventListener('click', function() {
            // In a real implementation, this would load the actual video
            this.innerHTML = `
                <div style="padding: 40px;">
                    <h3>🎬 Video zostanie wkrótce załadowane</h3>
                    <p>Przygotowujemy dla Ciebie ekskluzywne szkolenie...</p>
                    <div style="margin-top: 20px;">
                        <div style="width: 100%; height: 4px; background: rgba(255,255,255,0.2); border-radius: 2px; overflow: hidden;">
                            <div style="width: 0%; height: 100%; background: #667eea; border-radius: 2px; animation: loading 3s ease-in-out infinite;" id="loading-bar"></div>
                        </div>
                    </div>
                </div>
            `;
            
            // Add loading animation
            const style = document.createElement('style');
            style.textContent = `
                @keyframes loading {
                    0% { width: 0%; }
                    50% { width: 70%; }
                    100% { width: 100%; }
                }
            `;
            document.head.appendChild(style);
            
            trackEvent('Video_Placeholder_Click');
        });
    }
}

// Form handling
function initFormHandling() {
    // This would handle any forms on the page
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            handleFormSubmission(this);
        });
    });
}

// Show consultation form modal
function showConsultationForm() {
    // Create modal overlay
    const modal = document.createElement('div');
    modal.className = 'consultation-modal';
    modal.innerHTML = `
        <div class="modal-overlay">
            <div class="modal-content">
                <button class="modal-close">&times;</button>
                <h2>Umów się na bezpłatną konsultację</h2>
                <form class="consultation-form">
                    <div class="form-group">
                        <label for="name">Imię i nazwisko *</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="email">Email *</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="phone">Telefon *</label>
                        <input type="tel" id="phone" name="phone" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="business">Rodzaj działalności</label>
                        <select id="business" name="business">
                            <option value="">Wybierz...</option>
                            <option value="ecommerce">E-commerce</option>
                            <option value="services">Usługi</option>
                            <option value="consulting">Konsulting</option>
                            <option value="manufacturing">Produkcja</option>
                            <option value="other">Inne</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="challenges">Największe wyzwania energetyczne</label>
                        <textarea id="challenges" name="challenges" rows="3" placeholder="Opisz swoje główne problemy z energią, stresem lub koncentracją..."></textarea>
                    </div>
                    
                    <div class="form-group checkbox-group">
                        <label>
                            <input type="checkbox" name="consent" required>
                            Zgadzam się na przetwarzanie danych osobowych w celu kontaktu *
                        </label>
                    </div>
                    
                    <button type="submit" class="submit-btn">Umów konsultację</button>
                </form>
            </div>
        </div>
    `;
    
    // Add modal styles
    const modalStyles = document.createElement('style');
    modalStyles.textContent = `
        .consultation-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1000;
            animation: fadeIn 0.3s ease;
        }
        
        .modal-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            animation: slideIn 0.3s ease;
        }
        
        .modal-close {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 2rem;
            cursor: pointer;
            color: #666;
        }
        
        .consultation-form .form-group {
            margin-bottom: 20px;
        }
        
        .consultation-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .consultation-form input,
        .consultation-form select,
        .consultation-form textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .consultation-form input:focus,
        .consultation-form select:focus,
        .consultation-form textarea:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .checkbox-group label {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            font-size: 0.9rem;
        }
        
        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin: 0;
        }
        
        .submit-btn {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    `;
    
    document.head.appendChild(modalStyles);
    document.body.appendChild(modal);
    
    // Handle modal close
    const closeBtn = modal.querySelector('.modal-close');
    const overlay = modal.querySelector('.modal-overlay');
    
    closeBtn.addEventListener('click', () => closeModal(modal));
    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) closeModal(modal);
    });
    
    // Handle form submission
    const form = modal.querySelector('.consultation-form');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        handleConsultationForm(this, modal);
    });
}

// Close modal
function closeModal(modal) {
    modal.style.animation = 'fadeOut 0.3s ease';
    setTimeout(() => {
        document.body.removeChild(modal);
    }, 300);
    
    // Add fadeOut animation
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
    `;
    document.head.appendChild(style);
}

// Handle consultation form submission
function handleConsultationForm(form, modal) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    // Show loading state
    const submitBtn = form.querySelector('.submit-btn');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Wysyłanie...';
    submitBtn.disabled = true;
    
    // Simulate form submission (in real app, this would be an API call)
    setTimeout(() => {
        // Track successful form submission
        trackEvent('Consultation_Form_Submit', data);
        
        // Show success message
        form.innerHTML = `
            <div style="text-align: center; padding: 40px 20px;">
                <div style="font-size: 4rem; margin-bottom: 20px;">✅</div>
                <h3 style="color: #2ecc71; margin-bottom: 15px;">Dziękujemy!</h3>
                <p>Twoja prośba o konsultację została wysłana. Skontaktujemy się z Tobą w ciągu 24 godzin.</p>
                <button onclick="closeModal(this.closest('.consultation-modal'))" style="margin-top: 20px; padding: 10px 20px; background: #2ecc71; color: white; border: none; border-radius: 5px; cursor: pointer;">Zamknij</button>
            </div>
        `;
    }, 2000);
}

// Handle form submission
function handleFormSubmission(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    trackEvent('Form_Submit', {
        form_id: form.id || 'unknown',
        fields: Object.keys(data)
    });
    
    console.log('Form submitted:', data);
}

// Get page section for tracking
function getPageSection(element) {
    const sections = ['hero-section', 'content-section', 'video-container', 'benefits-section', 'cta-section'];
    
    for (let section of sections) {
        if (element.closest('.' + section)) {
            return section;
        }
    }
    
    return 'unknown';
}

// Analytics tracking
function initAnalytics() {
    // Track page view
    trackEvent('Page_View', {
        page: 'start',
        timestamp: new Date().toISOString(),
        user_agent: navigator.userAgent,
        referrer: document.referrer
    });
    
    // Track scroll depth
    let maxScroll = 0;
    window.addEventListener('scroll', function() {
        const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
        if (scrollPercent > maxScroll) {
            maxScroll = scrollPercent;
            
            // Track milestone scrolls
            if (maxScroll >= 25 && maxScroll < 50) {
                trackEvent('Scroll_25');
            } else if (maxScroll >= 50 && maxScroll < 75) {
                trackEvent('Scroll_50');
            } else if (maxScroll >= 75 && maxScroll < 100) {
                trackEvent('Scroll_75');
            } else if (maxScroll >= 100) {
                trackEvent('Scroll_100');
            }
        }
    });
}

// Track events (placeholder for real analytics)
function trackEvent(eventName, eventData = {}) {
    console.log('Event tracked:', eventName, eventData);
    
    // In a real implementation, this would send data to analytics services
    // like Google Analytics, Facebook Pixel, etc.
    
    // Example for Facebook Pixel:
    // if (typeof fbq !== 'undefined') {
    //     fbq('track', eventName, eventData);
    // }
    
    // Example for Google Analytics:
    // if (typeof gtag !== 'undefined') {
    //     gtag('event', eventName, eventData);
    // }
}

// Utility function to add CSS animations
function addAnimationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .animate-on-scroll {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .animate-on-scroll.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
        
        .step-card.animate-on-scroll {
            transform: translateY(50px) scale(0.95);
        }
        
        .step-card.animate-in {
            transform: translateY(0) scale(1);
        }
    `;
    document.head.appendChild(style);
}

// Initialize animation styles
addAnimationStyles();
