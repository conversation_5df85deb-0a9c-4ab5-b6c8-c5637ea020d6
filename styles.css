/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Manrope', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 100px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="1" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="1" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.hero-title {
    font-size: 3rem;
    font-weight: 800;
    margin-bottom: 30px;
    line-height: 1.2;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    position: relative;
    z-index: 1;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 40px;
    opacity: 0.95;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    z-index: 1;
    font-weight: 400;
}

/* Content Section */
.content-section {
    padding: 80px 0;
    background: white;
    position: relative;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 30px;
    text-align: center;
    color: #2c3e50;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 2px;
}

/* Step Cards */
.step-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 50px;
    margin-top: 60px;
}

.step-card {
    background: #ffffff;
    padding: 50px 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    position: relative;
    overflow: hidden;
}

.step-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.step-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.step-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    font-weight: 800;
    margin: 0 auto 30px;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.step-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.step-description {
    font-size: 1.1rem;
    color: #666;
    line-height: 1.7;
    font-weight: 400;
}

/* Video Section */
.video-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 80px 0;
    text-align: center;
    position: relative;
}

.video-placeholder {
    background: rgba(52, 73, 94, 0.8);
    border-radius: 20px;
    padding: 100px 40px;
    margin: 40px 0;
    color: white;
    border: 2px dashed rgba(255,255,255,0.3);
    transition: all 0.3s ease;
}

.video-placeholder:hover {
    border-color: rgba(255,255,255,0.6);
    background: rgba(52, 73, 94, 0.9);
}

.video-placeholder h3 {
    font-size: 2rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.video-placeholder p {
    font-size: 1.1rem;
    opacity: 0.8;
}

/* Benefits Section */
.benefits-section {
    padding: 100px 0;
    background: linear-gradient(135deg, #ecf0f1 0%, #bdc3c7 100%);
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 40px;
    margin-top: 60px;
}

.benefit-item {
    background: white;
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-top: 4px solid transparent;
}

.benefit-item:nth-child(1) { border-top-color: #f39c12; }
.benefit-item:nth-child(2) { border-top-color: #e74c3c; }
.benefit-item:nth-child(3) { border-top-color: #3498db; }
.benefit-item:nth-child(4) { border-top-color: #2ecc71; }

.benefit-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.benefit-icon {
    font-size: 4rem;
    margin-bottom: 25px;
    display: block;
}

.benefit-title {
    font-size: 1.4rem;
    font-weight: 700;
    margin-bottom: 20px;
    color: #2c3e50;
}

.benefit-item p {
    color: #666;
    font-size: 1rem;
    line-height: 1.6;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 100px 0;
    text-align: center;
    position: relative;
}

.cta-button {
    display: inline-block;
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    padding: 25px 50px;
    font-size: 1.3rem;
    font-weight: 700;
    text-decoration: none;
    border-radius: 50px;
    transition: all 0.3s ease;
    margin-top: 40px;
    text-transform: uppercase;
    letter-spacing: 2px;
    box-shadow: 0 10px 25px rgba(231, 76, 60, 0.3);
    position: relative;
    overflow: hidden;
}

.cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.cta-button:hover::before {
    left: 100%;
}

.cta-button:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(231, 76, 60, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.2rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .step-container {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .step-card {
        padding: 40px 30px;
    }
    
    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 30px;
    }
    
    .container {
        padding: 0 15px;
    }
    
    .cta-button {
        padding: 20px 40px;
        font-size: 1.1rem;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 1.8rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .step-card {
        padding: 30px 20px;
    }
    
    .benefit-item {
        padding: 30px 20px;
    }
}
